"""
联想Filez API配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """API配置类"""
    
    # API基础配置
    BASE_URL = "https://api.box.lenovo.com/v2"
    
    # OAuth配置
    APP_KEY = os.getenv('LENOVO_APP_KEY', 'aaa')  # 从环境变量获取，默认为示例值
    APP_SECRET = os.getenv('LENOVO_APP_SECRET', 'bbb')  # 从环境变量获取，默认为示例值
    
    # 用户配置
    USERNAME = os.getenv('LENOVO_USERNAME', 'admin')  # 管理员用户名
    
    # API端点
    ENDPOINTS = {
        'token': f"{BASE_URL}/oauth/token",
        'file_list': f"{BASE_URL}/api/file",
        'file_info_by_path': f"{BASE_URL}/api/file/path",
        'file_info_by_id': f"{BASE_URL}/api/file",
        'file_download': f"{BASE_URL}/api/file/content/download"
    }
    
    # 默认请求参数
    DEFAULT_PAGE_SIZE = 100
    DEFAULT_PATH_TYPE = 'ent'  # 企业空间
    
    # 备份配置
    BACKUP_DIR = os.getenv('BACKUP_DIR', './backups')
    BACKUP_LOG_FILE = os.path.join(BACKUP_DIR, 'backup_log.json')
    
    # 请求配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3
    
    @classmethod
    def get_auth_header(cls):
        """获取基础认证头"""
        import base64
        credentials = f"{cls.APP_KEY}:{cls.APP_SECRET}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
